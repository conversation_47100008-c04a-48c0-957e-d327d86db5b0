# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run tests (if testing dependencies are added)
npm test
```

## Architecture Overview

This is a **Next.js 14** application with **Supabase** backend integration, featuring:

### Core Technologies
- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **UI Framework**: Material-UI (MUI) with custom components
- **State Management**: Zustand stores + React Query for server state
- **Backend**: Supabase with PostgreSQL database
- **Authentication**: Supabase Auth with cookie-based sessions
- **File Upload**: Uppy.js for file management

### Key Architectural Patterns

1. **Job-Task Hierarchy**: Jobs contain multiple Tasks for batch processing
2. **Variable System**: Hierarchical variable management with inheritance
3. **API Routes**: Next.js API routes in `/app/api/` directory
4. **Component Structure**: Modular components with hooks for business logic

### Directory Structure Highlights

- `/app/`: Next.js App Router pages and API routes
- `/components/`: Reusable React components
- `/lib/`: Utilities, hooks, and services
- `/types/`: TypeScript type definitions
- `/__tests__/`: Test files (currently limited)
- `/supabase/`: Database migrations and configuration

### Variable System Architecture

The variable system manages hierarchical configuration with:
- **Template variables**: Base definitions in global_job_templates.vars
- **Category variables**: Overrides in categories.variable_overrides  
- **Dataset variables**: Specific overrides in datasets.variable_overrides
- **Inheritance**: Variables follow clear hierarchy with override mechanisms

Key files:
- `types/variable.ts`: Central type definitions
- `lib/utils/variableState.ts`: State management utilities
- `lib/hooks/useVariableTree.ts`: Data fetching hook
- `components/template/VariableTreeView.tsx`: Main UI component

### API Structure

API endpoints follow REST patterns:
- `/api/jobs/`: Job management
- `/api/tasks/`: Task operations  
- `/api/variable-tree/`: Variable system endpoints
- `/api/templates/`: Template management
- `/api/datasets/`: Dataset operations

### Testing Setup

Currently uses Jest + React Testing Library for:
- Component tests in `/__tests__/components/`
- Hook tests in `/__tests__/hooks/`
- Test files follow `.test.tsx` naming convention

### Development Notes

- Uses absolute imports with `@/*` path mapping
- Strict TypeScript mode enabled
- Tailwind CSS for styling with MUI components
- Supabase client configured for both server and client
- Environment variables required: `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### Common Development Tasks

1. **Adding new API endpoints**: Create route files in `/app/api/`
2. **Creating components**: Follow patterns in `/components/` directory
3. **Variable system changes**: Update types in `types/variable.ts` first
4. **Database changes**: Add migrations in `/supabase/migrations/`
5. **State management**: Use existing Zustand patterns or React Query hooks