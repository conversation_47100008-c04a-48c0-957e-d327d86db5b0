import React from 'react';
import { <PERSON>, <PERSON>, Tooltip, Button, Typography } from '@mui/material';
import { VariableWithContext } from '@/types/variable';
import InputRenderer from './InputRenderer';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableFocusIndicator } from './VariableFocusIndicator';

interface VariableInputRendererProps {
  variable: VariableWithContext;
  onChange: (variableName: string, newValue: any) => void;
  template_data?: any;
  contextInfo?: {
    nodeId?: number;
    nodeType?: 'category' | 'dataset';
    nodeName?: string;
  };
  showStatusBadge?: boolean;
  variableState?: {
    primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    secondaryState?: 'active' | 'overridden' | 'not-set';
    counts: {
      active: number;
      overridden: number;
      notSet: number;
      definedHigher: number;
      total: number;
    };
    activeVariable?: any;
    overriddenBy?: string[];
  };
  currentValue?: any; // Current value from state management (includes changes)
  // Indicates there is a local, unsaved override pending for this variable
  pendingOverride?: boolean;
  pendingReset?: boolean;
  // Focus functionality props
  focusVariable?: (variableName: string) => void;
  unfocusVariable?: () => void;
  isVariableFocused?: (variableName: string) => boolean;
  // Override/Reset functionality
  onOverride?: (variableName: string) => void;
  onReset?: (variableName: string) => void;
  onGoToDefining?: (variableName: string) => void;
}

/**
 * Renders a Variable Tree variable as an input component
 * Converts VariableWithContext to TemplateVariable format and uses InputRenderer
 */
export const VariableInputRenderer: React.FC<VariableInputRendererProps> = ({
  variable,
  onChange,
  template_data,
  contextInfo,
  showStatusBadge = true,
  variableState,
  currentValue,
  focusVariable,
  unfocusVariable,
  isVariableFocused,
  onOverride,
  onReset,
  onGoToDefining,
  pendingOverride,
  pendingReset
}) => {
  // Check if variable is editable at current level
  const isEditableHere = variable.editable_here !== false; // Default to true if not specified
  const isInherited = variableState?.primaryState === 'defined-higher';
  const effectivePrimaryState = pendingReset
    ? 'defined-higher'
    : ((pendingOverride && isInherited) ? 'active' : variableState?.primaryState);
  const disabledInput = pendingReset
    ? true
    : ((!isEditableHere) || (isInherited && !pendingOverride));

  // Convert VariableWithContext to TemplateVariable format
  // Use currentValue if provided (from state management), otherwise use original value
  const templateVariable = {
    name: variable.name,
    data: currentValue !== undefined ? currentValue : (variable.value || variable.data),
    links: variable.links || [],
    gui: variable.gui
  };



  // If no GUI config, show as status badge only
  if (!variable.gui?.component_id) {
    if (!showStatusBadge) return null;
    
    return (
      <Tooltip title={`${variable.name}: ${JSON.stringify(variable.value)}`}>
        <Chip
          label={variable.name}
          size="small"
          color={variable.is_active ? 'primary' : 'default'}
          variant={variable.is_overridden ? 'outlined' : 'filled'}
        />
      </Tooltip>
    );
  }

  return (
    <Box
      sx={{ mb: 2 }}
      data-variable-name={variable.name}
    >
      {/* Status indicator using proper VariableStatusBadge */}
      {showStatusBadge && variableState && (
        <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            onClick={(e) => {
              e.stopPropagation();
              if (focusVariable && unfocusVariable && isVariableFocused) {
                if (isVariableFocused(variable.name)) {
                  unfocusVariable();
                } else {
                  focusVariable(variable.name);
                }
              }
            }}
            sx={{
              cursor: focusVariable ? 'pointer' : 'default',
              display: 'inline-block'
            }}
          >
            <VariableStatusBadge
              variableName={variable.name}
              primaryState={effectivePrimaryState || variableState.primaryState}
              secondaryState={variableState.secondaryState}
              counts={variableState.counts}
              activeVariable={variableState.activeVariable}
              overriddenBy={variableState.overriddenBy}
              size="small"
              showTooltip={true}
              showActions={true}
              onOverride={onOverride}
              onGoToDefining={onGoToDefining}
            />
          </Box>
          {/* Focus indicator right next to the status badge */}
          {isVariableFocused && isVariableFocused(variable.name) && (
            <VariableFocusIndicator
              isVisible={true}
              primaryState={variableState.primaryState}
              size={12}
            />
          )}
        </Box>
      )}

      {/* Input component */}
      <Box sx={{ position: 'relative' }}>
        <InputRenderer
          variable={templateVariable} // Use templateVariable which already has the correct currentValue
          template_data={template_data}
          onChange={onChange}
          disabled={disabledInput}
        />

        {/* Override indicator */}
        {variable.is_overridden && (
          <Box
            sx={{
              position: 'absolute',
              top: -8,
              right: -8,
              width: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: 'warning.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.6rem',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            !
          </Box>
        )}
      </Box>

      {/* Override/Reset controls */}
      {(!isEditableHere || (isInherited && !pendingOverride)) && isInherited && onOverride && (
        <Box sx={{ mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => onOverride(variable.name)}
            sx={{ fontSize: '0.75rem' }}
          >
            Override at this level
          </Button>
        </Box>
      )}

      {(!disabledInput) && (effectivePrimaryState === 'active') && onReset && (
        <Box sx={{ mt: 1 }}>
          <Button
            size="small"
            variant="text"
            color="secondary"
            onClick={() => onReset(variable.name)}
            sx={{ fontSize: '0.75rem' }}
          >
            Reset to inherited
          </Button>
        </Box>
      )}

      {/* Constraint info for disabled variables */}
      {(!isEditableHere) && variable.meta?.constraints && (
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
          Editable at levels {variable.meta.constraints.minLevel}-{variable.meta.constraints.maxLevel}
        </Typography>
      )}
    </Box>
  );
};



export default VariableInputRenderer;
